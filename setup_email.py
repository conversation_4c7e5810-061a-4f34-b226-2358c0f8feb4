#!/usr/bin/env python3
"""
Email Setup Script for Amazora
Helps configure and test email functionality
"""

import os
import sys

def setup_email_config():
    """Setup email configuration"""
    print("🛍️ Amazora Email Setup")
    print("=" * 50)
    
    print("\n📧 Email Configuration")
    print("To use Gmail for sending emails, you need to:")
    print("1. Enable 2-Factor Authentication on your Gmail account")
    print("2. Generate an App Password")
    print("3. Use the App Password instead of your regular password")
    
    print("\n🔧 Steps to generate Gmail App Password:")
    print("1. Go to your Google Account settings")
    print("2. Navigate to Security > 2-Step Verification")
    print("3. Scroll down to 'App passwords'")
    print("4. Generate a new app password for 'Mail'")
    print("5. Use this password in the EMAIL_PASSWORD environment variable")
    
    print("\n💡 Alternative: Use environment variable")
    print("Set EMAIL_PASSWORD environment variable:")
    
    if sys.platform == "win32":
        print("set EMAIL_PASSWORD=your-app-password")
    else:
        print("export EMAIL_PASSWORD=your-app-password")
    
    print("\n🔍 Current Configuration:")
    print(f"Email: <EMAIL>")
    print(f"SMTP Server: smtp.gmail.com")
    print(f"Port: 587")
    print(f"TLS: Enabled")
    
    # Check if password is set
    password = os.environ.get('EMAIL_PASSWORD')
    if password:
        print(f"✅ EMAIL_PASSWORD is set")
    else:
        print("❌ EMAIL_PASSWORD is not set")
        print("Please set the EMAIL_PASSWORD environment variable")
    
    print("\n🧪 Test Email Functionality")
    print("To test email functionality:")
    print("1. Set EMAIL_PASSWORD environment variable")
    print("2. Run the Flask application")
    print("3. Register a new user or place an order")
    print("4. Check the console for email sending logs")

def test_email_connection():
    """Test email connection"""
    print("\n🔬 Testing Email Connection...")
    
    try:
        from email_service import init_email_service
        from flask import Flask
        
        # Create a test Flask app
        test_app = Flask(__name__)
        test_app.config['MAIL_SERVER'] = 'smtp.gmail.com'
        test_app.config['MAIL_PORT'] = 587
        test_app.config['MAIL_USE_TLS'] = True
        test_app.config['MAIL_USERNAME'] = '<EMAIL>'
        test_app.config['MAIL_PASSWORD'] = os.environ.get('EMAIL_PASSWORD', 'test')
        test_app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'
        
        init_email_service(test_app)
        
        with test_app.app_context():
            from flask_mail import Mail
            mail = Mail(test_app)
            
            # Test connection
            print("✅ Email service initialized successfully")
            print("📧 Ready to send emails!")
            
    except Exception as e:
        print(f"❌ Email setup failed: {e}")
        print("Please check your email configuration")

if __name__ == "__main__":
    setup_email_config()
    
    # Ask if user wants to test connection
    response = input("\nWould you like to test the email connection? (y/n): ")
    if response.lower() in ['y', 'yes']:
        test_email_connection()
    
    print("\n✨ Setup complete!")
    print("Run 'python app.py' to start the application")



