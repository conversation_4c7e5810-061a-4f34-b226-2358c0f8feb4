<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - Amazora</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .success-text {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .order-number {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .order-summary {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 1px solid #bbf7d0;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .summary-title {
            font-size: 18px;
            font-weight: 600;
            color: #166534;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .summary-title::before {
            content: "📋";
            margin-right: 10px;
        }
        
        .order-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #bbf7d0;
        }
        
        .detail-label {
            font-weight: 500;
            color: #166534;
        }
        
        .detail-value {
            font-weight: 600;
            color: #2d3748;
        }
        
        .total-row {
            border-top: 2px solid #10b981;
            border-bottom: none;
            font-size: 18px;
            font-weight: 700;
            color: #166534;
            padding-top: 15px;
            margin-top: 10px;
        }
        
        .order-items {
            margin: 30px 0;
        }
        
        .items-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .items-title::before {
            content: "🛍️";
            margin-right: 10px;
        }
        
        .item {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #f8fafc;
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .item-image {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 20px;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .item-meta {
            font-size: 14px;
            color: #718096;
        }
        
        .item-price {
            font-weight: 600;
            color: #10b981;
        }
        
        .next-steps {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 1px solid #bfdbfe;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .steps-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .steps-title::before {
            content: "📦";
            margin-right: 10px;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .step-number {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 5px;
        }
        
        .step-desc {
            font-size: 14px;
            color: #475569;
        }
        
        .cta-section {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
            transition: all 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
        }
        
        .secondary-button {
            display: inline-block;
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 14px;
            margin-left: 15px;
            transition: all 0.3s ease;
        }
        
        .footer {
            background-color: #2d3748;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .footer-text {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 20px;
        }
        
        .contact-info {
            font-size: 14px;
            opacity: 0.9;
        }
        
        @media (max-width: 600px) {
            .order-details {
                grid-template-columns: 1fr;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .cta-button, .secondary-button {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🛍️ Amazora</div>
            <div class="success-text">Order Confirmed!</div>
            <div class="order-number">Order #{{ order.id }}</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">Hello {{ user.username }}! 🎉</div>
            
            <div class="message">
                Thank you for your order! We're excited to let you know that your order has been confirmed and is being prepared for shipment. 
                You'll receive tracking information once your package is on its way.
            </div>
            
            <!-- Order Summary -->
            <div class="order-summary">
                <div class="summary-title">Order Summary</div>
                <div class="order-details">
                    <div class="detail-item">
                        <span class="detail-label">Order Number:</span>
                        <span class="detail-value">#{{ order.id }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Order Date:</span>
                        <span class="detail-value">{{ order.created_at.strftime('%B %d, %Y') }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Payment Method:</span>
                        <span class="detail-value">
                            {% if order.payment_method == 'instapay' %}
                                💳 Instapay
                            {% else %}
                                🚚 Pay After Shipping
                            {% endif %}
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Order Status:</span>
                        <span class="detail-value">
                            {% if order.status == 'pending' %}
                                ⏳ Pending
                            {% elif order.status == 'confirmed' %}
                                ✅ Confirmed
                            {% else %}
                                {{ order.status.title() }}
                            {% endif %}
                        </span>
                    </div>
                    <div class="detail-item total-row">
                        <span class="detail-label">Total Amount:</span>
                        <span class="detail-value">${{ "%.2f"|format(order.total_amount) }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Order Items -->
            <div class="order-items">
                <div class="items-title">Items Ordered</div>
                {% for item in order.items %}
                <div class="item">
                    <div class="item-image">📦</div>
                    <div class="item-details">
                        <div class="item-name">{{ item.product.name }}</div>
                        <div class="item-meta">Quantity: {{ item.quantity }} × ${{ "%.2f"|format(item.price) }}</div>
                    </div>
                    <div class="item-price">${{ "%.2f"|format(item.price * item.quantity) }}</div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Next Steps -->
            <div class="next-steps">
                <div class="steps-title">What's Next?</div>
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">Order Processing</div>
                        <div class="step-desc">We're preparing your order for shipment within 1-2 business days.</div>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">Shipping Notification</div>
                        <div class="step-desc">You'll receive tracking information once your package ships.</div>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">Delivery</div>
                        <div class="step-desc">Your order will be delivered to your shipping address.</div>
                    </div>
                </div>
            </div>
            
            <!-- Call to Action -->
            <div class="cta-section">
                <a href="{{ profile_url }}" class="cta-button">
                    📋 View Order Details
                </a>
                <a href="{{ shop_url }}" class="secondary-button">
                    🛒 Continue Shopping
                </a>
            </div>
            
            <div class="message">
                <strong>Need help?</strong><br>
                If you have any questions about your order, please don't hesitate to contact our customer support team. 
                We're here to help ensure you have the best shopping experience possible.
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-title">Thank you for choosing Amazora!</div>
            <div class="footer-text">
                We appreciate your business and look forward to serving you again.
            </div>
            <div class="contact-info">
                📧 <EMAIL><br>
                📞 +1 (555) 123-4567<br>
                🌐 www.amazora.com
            </div>
        </div>
    </div>
</body>
</html>



