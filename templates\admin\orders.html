{% extends "admin/base.html" %}

{% block title %}Orders - Admin{% endblock %}
{% block page_title %}Orders{% endblock %}

{% block content %}
<div class="admin-card bg-white rounded-xl p-6 shadow-lg">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">All Orders</h3>
        <span class="text-sm text-gray-500">{{ orders|length }} orders</span>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order #</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3"></th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for order in orders %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900">#{{ order.id }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-gray-900">{{ order.user.username }}</div>
                        <div class="text-gray-500 text-sm">{{ order.user.email }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-gray-900">${{ "%.2f"|format(order.total_amount) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if order.payment_method == 'instapay' %}
                            <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">Instapay</span>
                        {% else %}
                            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">Pay After Shipping</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs rounded {{ 'bg-yellow-100 text-yellow-800' if order.status=='pending' else 'bg-blue-100 text-blue-800' if order.status=='confirmed' else 'bg-green-100 text-green-800' if order.status=='delivered' else 'bg-purple-100 text-purple-800' if order.status=='shipped' else 'bg-red-100 text-red-800' }}">{{ order.status.title() }}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-gray-500">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                        <a class="px-3 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700" href="{{ url_for('admin_order_detail', order_id=order.id) }}">View</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}





