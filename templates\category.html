{% extends "base.html" %}

{% block title %}{{ category.name }} - Amazora{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
     Category Header 
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ category.name }}</h1>
        <p class="text-xl text-gray-600">{{ category.description }}</p>
    </div>

     Filters 
    <div class="flex flex-wrap items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">{{ products|length }} products</span>
        </div>
        <div class="flex items-center space-x-4">
            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500">
                <option>Sort by: Featured</option>
                <option>Price: Low to High</option>
                <option>Price: High to Low</option>
                <option>Newest</option>
            </select>
        </div>
    </div>

     Products Grid 
    {% if products %}
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {% for product in products %}
        <div class="group product-card">
            <div class="relative overflow-hidden rounded-xl bg-gray-100 aspect-square mb-4">
                <img src="{{ product.image_url or '/placeholder.svg?height=300&width=300&query=' + product.name }}" 
                     alt="{{ product.name }}" 
                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                <button class="absolute top-4 right-4 w-10 h-10 bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-pink-50"
                        onclick="addToCart({{ product.id }}, 1)">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4"></path>
                    </svg>
                </button>
            </div>
            <div class="text-center">
                <h3 class="font-semibold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors duration-300">
                    <a href="{{ url_for('product', slug=product.slug) }}">{{ product.name }}</a>
                </h3>
                <p class="text-2xl font-bold text-indigo-900">${{ "%.2f"|format(product.price) }}</p>
                {% if product.stock < 5 %}
                <p class="text-sm text-red-600 mt-1">Only {{ product.stock }} left!</p>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-16">
        <h2 class="text-2xl font-semibold text-gray-900">No products found</h2>
        <p class="mt-2 text-gray-600">Check back soon for new arrivals</p>
    </div>
    {% endif %}
</div>
{% endblock %}
