// Main JavaScript for Amazora
class AmazoraApp {
    constructor() {
        this.cartCount = 0;
        this.init();
    }

    init() {
        this.initLoadingScreen();
        this.setupEventListeners();
        this.updateCartCount();
        this.initAnimations();
        initScrollAnimations();
        initIconAnimations();
    }

    initLoadingScreen() {
        // Show loading screen for shorter time - optimized for speed
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            // Add fade out animation after 500ms (much faster)
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                loadingScreen.style.transition = 'opacity 0.3s ease-out';

                // Remove loading screen after fade out
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }, 500);
        }
    }

    setupEventListeners() {
        // Mobile menu toggle with animation
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                // Toggle active class for icon animation
                mobileMenuBtn.classList.toggle('active');

                if (mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.remove('hidden');
                    mobileMenu.classList.add('animate-slide-in-left');
                } else {
                    mobileMenu.classList.add('animate-slide-in-right');
                    setTimeout(() => {
                        mobileMenu.classList.add('hidden');
                        mobileMenu.classList.remove('animate-slide-in-right');
                    }, 300);
                }
            });
        }

        // User menu toggle with animation
        const userMenuBtn = document.getElementById('user-menu-btn');
        const userMenu = document.getElementById('user-menu');

        if (userMenuBtn && userMenu) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();

                // Toggle active class for icon animation
                userMenuBtn.classList.toggle('active');

                if (userMenu.classList.contains('hidden')) {
                    userMenu.classList.remove('hidden');
                    userMenu.classList.add('animate-slide-in-right');
                } else {
                    userMenu.classList.add('hidden');
                    userMenu.classList.remove('animate-slide-in-right');
                }
            });

            // Close user menu when clicking outside
            document.addEventListener('click', () => {
                if (!userMenu.classList.contains('hidden')) {
                    userMenu.classList.add('hidden');
                    userMenu.classList.remove('animate-slide-in-right');
                    userMenuBtn.classList.remove('active');
                }
            });
        }

        // Cart drawer
        const cartBtn = document.getElementById('cart-btn');
        const cartDrawer = document.getElementById('cart-drawer');
        const cartOverlay = document.getElementById('cart-overlay');
        const closeCartBtn = document.getElementById('close-cart');
        const cartContent = document.getElementById('cart-content');

        if (cartBtn && cartDrawer) {
            cartBtn.addEventListener('click', () => {
                this.openCartDrawer();
            });
        }

        if (cartOverlay) {
            cartOverlay.addEventListener('click', () => {
                this.closeCartDrawer();
            });
        }

        if (closeCartBtn) {
            closeCartBtn.addEventListener('click', () => {
                this.closeCartDrawer();
            });
        }

        // Flash message auto-hide
        const flashMessages = document.querySelectorAll('.flash-message');
        flashMessages.forEach(message => {
            setTimeout(() => {
                message.style.opacity = '0';
                setTimeout(() => {
                    message.remove();
                }, 300);
            }, 5000);
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Newsletter form (target only explicit newsletter forms)
        const newsletterForm = document.querySelector('form#newsletter-form, form[data-newsletter="true"]');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleNewsletterSubmit(e.target);
            });
        }
    }

    openCartDrawer() {
        const cartDrawer = document.getElementById('cart-drawer');
        const cartContent = document.getElementById('cart-content');
        
        if (cartDrawer && cartContent) {
            cartDrawer.classList.remove('hidden');
            setTimeout(() => {
                cartContent.classList.remove('translate-x-full');
            }, 10);
            
            // Load cart items
            this.loadCartItems();
        }
    }

    closeCartDrawer() {
        const cartDrawer = document.getElementById('cart-drawer');
        const cartContent = document.getElementById('cart-content');
        
        if (cartDrawer && cartContent) {
            cartContent.classList.add('translate-x-full');
            setTimeout(() => {
                cartDrawer.classList.add('hidden');
            }, 300);
        }
    }

    async addToCart(productId, quantity = 1) {
        try {
            const response = await fetch('/add_to_cart', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: quantity
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showNotification('Product added to cart!', 'success');
                this.updateCartCount();
                this.animateCartButton();
            } else {
                this.showNotification('Failed to add product to cart', 'error');
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            this.showNotification('An error occurred', 'error');
        }
    }

    async loadCartItems() {
        try {
            const response = await fetch('/api/cart');
            const data = await response.json();
            
            const cartItemsContainer = document.getElementById('cart-items');
            const cartTotal = document.getElementById('cart-total');
            
            if (cartItemsContainer && data.items) {
                cartItemsContainer.innerHTML = '';
                let total = 0;
                
                data.items.forEach(item => {
                    const itemElement = this.createCartItemElement(item);
                    cartItemsContainer.appendChild(itemElement);
                    total += item.product.price * item.quantity;
                });
                
                if (cartTotal) {
                    cartTotal.textContent = total.toFixed(2);
                }
            }
        } catch (error) {
            console.error('Error loading cart items:', error);
        }
    }

    createCartItemElement(item) {
        const div = document.createElement('div');
        div.className = 'flex items-center space-x-4 py-4 border-b';
        div.innerHTML = `
            <img src="${item.product.image_url || '/diverse-products-still-life.png'}" 
                 alt="${item.product.name}" 
                 class="w-16 h-16 object-cover rounded-lg">
            <div class="flex-1">
                <h4 class="font-medium text-gray-900">${item.product.name}</h4>
                <p class="text-sm text-gray-500">$${item.product.price.toFixed(2)}</p>
            </div>
            <div class="flex items-center space-x-2">
                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors duration-200"
                        onclick="app.updateCartQuantity(${item.id}, ${item.quantity - 1})">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                    </svg>
                </button>
                <span class="w-8 text-center">${item.quantity}</span>
                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors duration-200"
                        onclick="app.updateCartQuantity(${item.id}, ${item.quantity + 1})">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </button>
            </div>
        `;
        return div;
    }

    async updateCartQuantity(itemId, newQuantity) {
        if (newQuantity <= 0) {
            return this.removeFromCart(itemId);
        }

        try {
            const response = await fetch('/update_cart', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId,
                    quantity: newQuantity
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.loadCartItems();
                this.updateCartCount();
            }
        } catch (error) {
            console.error('Error updating cart:', error);
        }
    }

    async removeFromCart(itemId) {
        try {
            const response = await fetch('/remove_from_cart', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.loadCartItems();
                this.updateCartCount();
                this.showNotification('Item removed from cart', 'success');
            }
        } catch (error) {
            console.error('Error removing from cart:', error);
        }
    }

    async updateCartCount() {
        try {
            const response = await fetch('/api/cart-count');
            const data = await response.json();
            
            const cartCountElement = document.getElementById('cart-count');
            if (cartCountElement && data.count !== undefined) {
                cartCountElement.textContent = data.count;
                this.cartCount = data.count;
            }
        } catch (error) {
            console.error('Error updating cart count:', error);
        }
    }

    animateCartButton() {
        const cartBtn = document.getElementById('cart-btn');
        const cartCount = document.getElementById('cart-count');

        if (cartBtn) {
            // Add cart updated class for special animation
            cartBtn.classList.add('cart-updated');

            // Animate the cart icon
            cartBtn.classList.add('animate-rubber-band');

            // Animate the cart count
            if (cartCount) {
                cartCount.classList.add('animate-heartbeat');
            }

            // Remove animation classes after animation completes
            setTimeout(() => {
                cartBtn.classList.remove('animate-rubber-band', 'cart-updated');
                if (cartCount) {
                    cartCount.classList.remove('animate-heartbeat');
                }
            }, 1000);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium transform translate-x-full transition-transform duration-300 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    handleNewsletterSubmit(form) {
        const email = form.querySelector('input[type="email"]').value;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (!email) {
            this.showNotification('Please enter your email address', 'error');
            return;
        }
        
        // Simulate API call
        submitBtn.textContent = 'Subscribing...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            this.showNotification('Thank you for subscribing!', 'success');
            form.reset();
            submitBtn.textContent = 'Subscribe';
            submitBtn.disabled = false;
        }, 1500);
    }

    initAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observe elements with animation class
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero-parallax');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
    }
}

// Global functions for inline event handlers
function addToCart(productId, quantity = 1) {
    if (window.app) {
        window.app.addToCart(productId, quantity);
    }
}

// Wishlist functionality
function toggleWishlist(productId) {
    const heartIcon = event.target.closest('button').querySelector('svg');

    // Animate the heart icon
    heartIcon.classList.add('animate-heartbeat');

    // Toggle filled state
    if (heartIcon.getAttribute('fill') === 'currentColor') {
        heartIcon.setAttribute('fill', 'none');
        heartIcon.style.color = '#6b7280';
        window.app?.showNotification('Removed from wishlist', 'info');
    } else {
        heartIcon.setAttribute('fill', 'currentColor');
        heartIcon.style.color = '#e91e63';
        window.app?.showNotification('Added to wishlist', 'success');
    }

    // Remove animation class
    setTimeout(() => {
        heartIcon.classList.remove('animate-heartbeat');
    }, 1000);
}

// Quick view functionality
function quickView(productId) {
    const button = event.target.closest('button');
    const icon = button.querySelector('svg');

    // Animate the icon
    icon.classList.add('animate-spin');

    // Simulate loading
    setTimeout(() => {
        icon.classList.remove('animate-spin');
        window.app?.showNotification('Quick view feature coming soon!', 'info');
    }, 500);
}

// Enhanced scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;

                // Add staggered animation delays for grid items
                if (element.classList.contains('product-card') || element.classList.contains('category-card')) {
                    const delay = Array.from(element.parentNode.children).indexOf(element) * 100;
                    element.style.animationDelay = `${delay}ms`;
                }

                element.classList.add('animate-fade-in-up');
                observer.unobserve(element);
            }
        });
    }, observerOptions);

    // Observe all animatable elements
    document.querySelectorAll('.animate-on-scroll, .product-card, .category-card').forEach(el => {
        observer.observe(el);
    });
}

// Enhanced icon interactions
function initIconAnimations() {
    // Add hover effects to all icons
    document.querySelectorAll('.icon-hover').forEach(icon => {
        icon.addEventListener('mouseenter', () => {
            icon.style.transform = 'scale(1.1)';
        });

        icon.addEventListener('mouseleave', () => {
            icon.style.transform = 'scale(1)';
        });
    });

    // Add click animations to buttons
    document.querySelectorAll('button').forEach(button => {
        button.addEventListener('click', (e) => {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            button.style.position = 'relative';
            button.style.overflow = 'hidden';
            button.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Add ripple animation CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AmazoraApp();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.app) {
        window.app.updateCartCount();
    }
});

// Service Worker registration for PWA capabilities
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/static/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
