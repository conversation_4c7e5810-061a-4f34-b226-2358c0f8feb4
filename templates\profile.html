{% extends "base.html" %}

{% block title %}My Profile - Amazora{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Profile Header -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex items-center space-x-6">
            <div class="w-24 h-24 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center overflow-hidden">
                {% if profile.avatar_url %}
                    <img src="{{ profile.avatar_url | image_url }}" alt="Avatar" class="w-full h-full object-cover">
                {% else %}
                    <i class="fas fa-user text-white text-3xl"></i>
                {% endif %}
            </div>
            <div class="flex-1">
                <h1 class="text-3xl font-bold text-gray-900">
                    {% if profile.first_name or profile.last_name %}
                        {{ profile.first_name or '' }} {{ profile.last_name or '' }}
                    {% else %}
                        {{ current_user.username }}
                    {% endif %}
                </h1>
                <p class="text-gray-600">{{ current_user.email }}</p>
                <p class="text-sm text-gray-500">Member since {{ current_user.created_at.strftime('%B %Y') }}</p>
            </div>
            <div>
                <a href="{{ url_for('edit_profile') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Profile
                </a>
            </div>
        </div>
    </div>

    <!-- Profile Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="bg-blue-100 rounded-full p-3 mr-4">
                    <i class="fas fa-shopping-bag text-blue-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Orders</p>
                    <p class="text-2xl font-bold text-gray-900">{{ orders|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="bg-green-100 rounded-full p-3 mr-4">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">Completed Orders</p>
                    <p class="text-2xl font-bold text-gray-900">{{ orders|selectattr('status', 'eq', 'delivered')|list|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="bg-yellow-100 rounded-full p-3 mr-4">
                    <i class="fas fa-star text-yellow-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">Reviews Written</p>
                    <p class="text-2xl font-bold text-gray-900">{{ reviews|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="bg-red-100 rounded-full p-3 mr-4">
                    <i class="fas fa-times-circle text-red-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">Cancelled Orders</p>
                    <p class="text-2xl font-bold text-gray-900">{{ orders|selectattr('status', 'eq', 'cancelled')|list|length }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" x-data="{ activeTab: 'orders' }">
                <button @click="activeTab = 'orders'" 
                        :class="activeTab === 'orders' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                    <i class="fas fa-shopping-bag mr-2"></i>
                    Order History
                </button>
                <button @click="activeTab = 'reviews'" 
                        :class="activeTab === 'reviews' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                    <i class="fas fa-star mr-2"></i>
                    My Reviews
                </button>
                <button @click="activeTab = 'address'" 
                        :class="activeTab === 'address' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                    <i class="fas fa-map-marker-alt mr-2"></i>
                    Address Info
                </button>
            </nav>
        </div>

        <!-- Orders Tab -->
        <div x-show="activeTab === 'orders'" class="p-6">
            {% if orders %}
                <div class="space-y-4">
                    {% for order in orders %}
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h3 class="font-semibold text-gray-900">Order #{{ order.id }}</h3>
                                <p class="text-sm text-gray-600">{{ order.created_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                                <div class="mt-1">
                                    {% if order.payment_method == 'instapay' %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-credit-card mr-1"></i>
                                            Instapay
                                        </span>
                                    {% elif order.payment_method == 'pay_after_shipping' %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-truck mr-1"></i>
                                            Pay After Shipping
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900">${{ "%.2f"|format(order.total_amount) }}</p>
                                {% if order.status == 'pending' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock mr-1"></i>
                                        Pending
                                    </span>
                                {% elif order.status == 'confirmed' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-check mr-1"></i>
                                        Confirmed
                                    </span>
                                {% elif order.status == 'shipped' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        <i class="fas fa-truck mr-1"></i>
                                        Shipped
                                    </span>
                                {% elif order.status == 'delivered' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Delivered
                                    </span>
                                {% elif order.status == 'cancelled' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Cancelled
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="space-y-2">
                            {% for item in order.items %}
                            <div class="flex items-center space-x-3 text-sm">
                                <div class="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                                    {% if item.product.image_url %}
                                        <img src="{{ item.product.image_url | image_url }}" alt="{{ item.product.name }}" class="w-full h-full object-cover">
                                    {% else %}
                                        <div class="w-full h-full bg-gradient-to-r from-gray-300 to-gray-400 flex items-center justify-center">
                                            <i class="fas fa-image text-gray-500"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium text-gray-900">{{ item.product.name }}</p>
                                    <p class="text-gray-600">Qty: {{ item.quantity }} × ${{ "%.2f"|format(item.price) }}</p>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="mt-4 flex space-x-2">
                            <a href="{{ url_for('order_confirmation', order_id=order.id) }}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                                View Details
                            </a>
                            {% if order.status in ['pending', 'confirmed'] %}
                                <form method="POST" action="{{ url_for('cancel_order', order_id=order.id) }}" class="inline" onsubmit="return confirm('Are you sure you want to cancel this order?')">
                                    <button type="submit" class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Cancel Order
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-shopping-bag text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No orders yet</h3>
                    <p class="text-gray-500 mb-4">Start shopping to see your orders here.</p>
                    <a href="{{ url_for('home') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        Start Shopping
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Reviews Tab -->
        <div x-show="activeTab === 'reviews'" class="p-6">
            {% if reviews %}
                <div class="space-y-4">
                    {% for review in reviews %}
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                                    {% if review.product.image_url %}
                                        <img src="{{ review.product.image_url | image_url }}" alt="{{ review.product.name }}" class="w-full h-full object-cover">
                                    {% else %}
                                        <div class="w-full h-full bg-gradient-to-r from-gray-300 to-gray-400 flex items-center justify-center">
                                            <i class="fas fa-image text-gray-500"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">{{ review.product.name }}</h3>
                                    <div class="flex items-center space-x-1">
                                        {% for i in range(1, 6) %}
                                            {% if i <= review.rating %}
                                                <i class="fas fa-star text-yellow-400"></i>
                                            {% else %}
                                                <i class="far fa-star text-gray-300"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="text-sm text-gray-600 ml-2">{{ review.created_at.strftime('%B %d, %Y') }}</span>
                                    </div>
                                </div>
                            </div>
                            <form method="POST" action="{{ url_for('delete_review', review_id=review.id) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this review?')">
                                <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                        {% if review.comment %}
                            <p class="text-gray-700">{{ review.comment }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-star text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No reviews yet</h3>
                    <p class="text-gray-500">Share your experience with products you've purchased.</p>
                </div>
            {% endif %}
        </div>

        <!-- Address Tab -->
        <div x-show="activeTab === 'address'" class="p-6">
            {% if profile.address %}
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900 mb-2">Shipping Address</h3>
                    <div class="text-gray-700">
                        {% if profile.first_name or profile.last_name %}
                            <p>{{ profile.first_name or '' }} {{ profile.last_name or '' }}</p>
                        {% endif %}
                        {% if profile.address %}
                            <p>{{ profile.address }}</p>
                        {% endif %}
                        {% if profile.city or profile.state or profile.zip_code %}
                            <p>
                                {{ profile.city or '' }}{% if profile.city and profile.state %}, {% endif %}{{ profile.state or '' }} {{ profile.zip_code or '' }}
                            </p>
                        {% endif %}
                        {% if profile.country %}
                            <p>{{ profile.country }}</p>
                        {% endif %}
                        {% if profile.phone %}
                            <p class="mt-2"><strong>Phone:</strong> {{ profile.phone }}</p>
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-map-marker-alt text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No address saved</h3>
                    <p class="text-gray-500 mb-4">Add your address to make checkout faster.</p>
                    <a href="{{ url_for('edit_profile') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Add Address
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
