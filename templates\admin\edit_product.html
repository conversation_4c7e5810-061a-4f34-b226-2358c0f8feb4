{% extends "admin/base.html" %}

{% block title %}Edit Product - Admin Dashboard{% endblock %}
{% block page_title %}Edit Product: {{ product.name }}{% endblock %}

{% block content %}
<div class="max-w-4xl">
    <div class="admin-card bg-white rounded-xl shadow-lg p-6">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Product Information</h3>
            <p class="text-gray-600">Update the product details below.</p>
        </div>

        <form method="POST" enctype="multipart/form-data" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Product Name *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ product.name }}"
                           required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                           placeholder="Enter product name">
                </div>

                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                        URL Slug *
                    </label>
                    <input type="text" 
                           id="slug" 
                           name="slug" 
                           value="{{ product.slug }}"
                           required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                           placeholder="product-url-slug">
                </div>
            </div>

            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                          placeholder="Enter product description">{{ product.description or '' }}</textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                        Price ($) *
                    </label>
                    <input type="number" 
                           id="price" 
                           name="price" 
                           value="{{ product.price }}"
                           step="0.01" 
                           min="0" 
                           required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                           placeholder="0.00">
                </div>

                <div>
                    <label for="stock" class="block text-sm font-medium text-gray-700 mb-2">
                        Stock Quantity *
                    </label>
                    <input type="number" 
                           id="stock" 
                           name="stock" 
                           value="{{ product.stock }}"
                           min="0" 
                           required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                           placeholder="0">
                </div>

                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Category *
                    </label>
                    <select id="category_id" 
                            name="category_id" 
                            required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200">
                        <option value="">Select a category</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if category.id == product.category_id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div>
                <label for="image_file" class="block text-sm font-medium text-gray-700 mb-2">
                    Product Image
                </label>

                <!-- Current Image -->
                {% if product.image_url %}
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-700 mb-2">Current Image:</p>
                    <img src="{{ product.image_url | image_url }}" alt="{{ product.name }}" class="w-32 h-32 object-cover rounded-lg border border-gray-300">
                </div>
                {% endif %}

                <div class="flex items-center justify-center w-full">
                    <label for="image_file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                            <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                            <p class="mb-2 text-sm text-gray-500">
                                <span class="font-semibold">Click to upload new image</span> or drag and drop
                            </p>
                            <p class="text-xs text-gray-500">PNG, JPG, JPEG, GIF or WebP (MAX. 16MB)</p>
                            <p class="text-xs text-gray-400 mt-1">Leave empty to keep current image</p>
                        </div>
                        <input id="image_file"
                               name="image_file"
                               type="file"
                               accept="image/*"
                               class="hidden"
                               onchange="previewImage(this)">
                    </label>
                </div>

                <!-- Image Preview -->
                <div id="image-preview" class="mt-4 hidden">
                    <p class="text-sm font-medium text-gray-700 mb-2">New Image Preview:</p>
                    <img id="preview-img" src="" alt="Preview" class="w-32 h-32 object-cover rounded-lg border border-gray-300">
                    <p id="file-name" class="text-sm text-gray-600 mt-2"></p>
                </div>
            </div>

            <div class="flex items-center">
                <input type="checkbox" 
                       id="featured" 
                       name="featured" 
                       {% if product.featured %}checked{% endif %}
                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                <label for="featured" class="ml-2 block text-sm text-gray-900">
                    Featured Product
                </label>
                <p class="ml-2 text-sm text-gray-500">(Will appear on the homepage)</p>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    <span class="text-sm font-medium text-blue-800">Product Statistics</span>
                </div>
                <div class="mt-2 text-sm text-blue-700">
                    Created: <strong>{{ product.created_at.strftime('%B %d, %Y at %I:%M %p') }}</strong>
                </div>
            </div>

            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{{ url_for('admin_products') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Products
                </a>
                <button type="submit" class="inline-flex items-center px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
                    <i class="fas fa-save mr-2"></i>
                    Update Product
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function previewImage(input) {
    const preview = document.getElementById('image-preview');
    const img = document.getElementById('preview-img');
    const fileName = document.getElementById('file-name');

    if (input.files && input.files[0]) {
        const file = input.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            img.src = e.target.result;
            fileName.textContent = `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            preview.classList.remove('hidden');
        };

        reader.readAsDataURL(file);
    } else {
        preview.classList.add('hidden');
    }
}
</script>
{% endblock %}
