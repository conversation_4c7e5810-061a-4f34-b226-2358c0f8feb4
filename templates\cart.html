{% extends "base.html" %}

{% block title %}Shopping Cart - Amazora{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">Shopping Cart</h1>
    
    {% if cart_items %}
    <div class="lg:grid lg:grid-cols-12 lg:gap-x-12 lg:items-start">
        <div class="lg:col-span-7">
            <div class="bg-white shadow rounded-lg">
                {% for item in cart_items %}
                <div class="flex items-center px-6 py-6 {% if not loop.last %}border-b border-gray-200{% endif %}" data-cart-item-id="{{ item.id }}">
                    <img src="{{ item.product.image_url | image_url or '/placeholder.svg?height=100&width=100&query=' + item.product.name }}" 
                         alt="{{ item.product.name }}" 
                         class="w-20 h-20 object-cover rounded-lg">
                    <div class="ml-6 flex-1">
                        <h3 class="text-lg font-medium text-gray-900">
                            <a href="{{ url_for('product', slug=item.product.slug) }}">{{ item.product.name }}</a>
                        </h3>
                        <p class="text-sm text-gray-500">{{ item.product.category.name }}</p>
                        <p class="text-lg font-semibold text-indigo-900 mt-2">${{ "%.2f"|format(item.product.price) }}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <button onclick="updateQuantity({{ item.id }}, {{ item.quantity - 1 }})" 
                                    class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors duration-200"
                                    {% if item.quantity <= 1 %}disabled{% endif %}>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                </svg>
                            </button>
                            <span class="w-12 text-center" id="quantity-{{ item.id }}">{{ item.quantity }}</span>
                            <button onclick="updateQuantity({{ item.id }}, {{ item.quantity + 1 }})" 
                                    class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors duration-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </button>
                        </div>
                        <button onclick="removeFromCart({{ item.id }})" class="text-red-600 hover:text-red-800 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="lg:col-span-5 mt-8 lg:mt-0">
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="font-medium">${{ "%.2f"|format(total) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Shipping</span>
                        <span class="font-medium">Free</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Tax</span>
                        <span class="font-medium">${{ "%.2f"|format(total * 0.08) }}</span>
                    </div>
                    <div class="border-t pt-3">
                        <div class="flex justify-between">
                            <span class="text-lg font-semibold">Total</span>
                            <span class="text-lg font-semibold">${{ "%.2f"|format(total * 1.08) }}</span>
                        </div>
                    </div>
                </div>
                {% if current_user.is_authenticated %}
                    <a href="{{ url_for('checkout') }}" class="w-full mt-6 bg-gradient-to-r from-indigo-900 to-pink-600 text-white py-3 px-4 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-300 flex items-center justify-center">
                        <i class="fas fa-lock mr-2"></i>
                        Proceed to Checkout
                    </a>
                {% else %}
                    <a href="{{ url_for('login') }}" class="w-full mt-6 bg-gradient-to-r from-indigo-900 to-pink-600 text-white py-3 px-4 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-300 flex items-center justify-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login to Checkout
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% else %}
    <div class="text-center py-16">
        <svg class="mx-auto h-24 w-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4"></path>
        </svg>
        <h2 class="mt-4 text-2xl font-semibold text-gray-900">Your cart is empty</h2>
        <p class="mt-2 text-gray-600">Start shopping to add items to your cart</p>
        <a href="{{ url_for('home') }}" class="mt-6 inline-block bg-gradient-to-r from-indigo-900 to-pink-600 text-white px-8 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-300">
            Continue Shopping
        </a>
    </div>
    {% endif %}
</div>

<script>
function updateQuantity(cartItemId, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(cartItemId);
        return;
    }
    
    fetch('/update_cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            cart_item_id: cartItemId,
            quantity: newQuantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the quantity display
            document.getElementById('quantity-' + cartItemId).textContent = newQuantity;
            // Trigger cart count update
            window.dispatchEvent(new Event('cartUpdated'));
            // Reload the page to update totals
            location.reload();
        } else {
            alert('Error updating cart: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating cart');
    });
}

function removeFromCart(cartItemId) {
    if (!confirm('Are you sure you want to remove this item from your cart?')) {
        return;
    }
    
    fetch('/remove_from_cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            cart_item_id: cartItemId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the item from the DOM
            const cartItem = document.querySelector(`[data-cart-item-id="${cartItemId}"]`);
            if (cartItem) {
                cartItem.remove();
            }
            // Trigger cart count update
            window.dispatchEvent(new Event('cartUpdated'));
            // Reload the page to update totals
            location.reload();
        } else {
            alert('Error removing item: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error removing item from cart');
    });
}
</script>
{% endblock %}
