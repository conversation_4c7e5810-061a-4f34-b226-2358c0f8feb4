<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Amazora - Shop Smart. Live Beautifully.{% endblock %}</title>
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/favicon.svg') }}">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/logo.png') }}">

    <!-- Performance optimizations -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" href="{{ url_for('static', filename='images/logo.png') }}" as="image">

    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}?v=1.1">
    <style>
        .rating-star {
            transition: all 0.2s ease-in-out;
        }
        .rating-star:hover {
            transform: scale(1.1);
        }
        .rating-star i {
            transition: color 0.2s ease-in-out;
        }
    </style>
</head>
<body class="font-poppins bg-gray-50">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center z-50">
        <div class="text-center">
            <div class="logo-loading mb-8">
                <img src="{{ url_for('static', filename='images/logo.png') }}"
                     alt="Amazora Logo"
                     class="h-50 sm:h-48 w-auto mx-auto animate-pulse">
            </div>
            <div class="loading-text">
                <h2 class="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-blue-600 to-indigo-900 bg-clip-text text-transparent mb-4">
                    Amazora
                </h2>
                <p class="text-lg text-gray-600 mb-6">Loading your shopping experience...</p>
            </div>
            <div class="loading-spinner">
                <div class="flex justify-center space-x-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full animate-bounce"></div>
                    <div class="w-3 h-3 bg-indigo-500 rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                    <div class="w-3 h-3 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16 sm:h-18 md:h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{{ url_for('home') }}" class="flex items-center space-x-4 group">
                        <img src="{{ url_for('static', filename='images/logo.png') }}"
                             alt="Amazora Logo"
                             class="h-28 sm:h-24 md:h-32 w-auto transition-transform duration-300 group-hover:scale-110 icon-hover logo-glow">
                        <span class="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-900 bg-clip-text text-transparent hidden sm:block">
                            Amazora
                        </span>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="{{ url_for('home') }}" class="nav-link">Home</a>
                        <a href="{{ url_for('category', slug='fashion') }}" class="nav-link">Fashion</a>
                        <a href="{{ url_for('category', slug='beauty') }}" class="nav-link">Beauty</a>
                        <a href="{{ url_for('category', slug='home') }}" class="nav-link">Home</a>
                        <a href="{{ url_for('category', slug='tech') }}" class="nav-link">Tech</a>
                        <a href="{{ url_for('category', slug='pharmacy') }}" class="nav-link">Pharmacy</a>
                    </div>
                </div>

                <!-- User Actions -->
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('cart') }}" id="cart-btn" class="cart-icon relative p-2 text-gray-600 hover:text-pink-600 transition-colors duration-300 icon-glow">
                        <svg class="w-6 h-6 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4"></path>
                        </svg>
                        <span id="cart-count" class="cart-count absolute -top-2 -right-2 bg-pink-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-bounce-in">0</span>
                    </a>

                    {% if current_user.is_authenticated %}
                        <div class="relative">
                            <button id="user-menu-btn" class="user-menu-icon flex items-center text-sm text-gray-600 hover:text-pink-600 transition-colors duration-300">
                                <span class="mr-2">{{ current_user.username }}</span>
                                <svg class="w-4 h-4 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 animate-slide-in-right">
                                <a href="{{ url_for('user_profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                                    <svg class="w-4 h-4 inline mr-2 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    My Profile
                                </a>
                                {% if current_user.is_admin %}
                                    <a href="{{ url_for('admin_dashboard') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                                        <svg class="w-4 h-4 inline mr-2 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        Admin Panel
                                    </a>
                                {% endif %}
                                <a href="{{ url_for('logout') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                                    <svg class="w-4 h-4 inline mr-2 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    Logout
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{{ url_for('login') }}" class="nav-icon text-gray-600 hover:text-pink-600 transition-colors duration-300 flex items-center">
                            <svg class="w-4 h-4 mr-1 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            Login
                        </a>
                        <a href="{{ url_for('register') }}" class="btn-icon bg-gradient-to-r from-indigo-900 to-pink-600 text-white px-4 py-2 rounded-lg hover:opacity-90 transition-all duration-300 icon-glow">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                            </svg>
                            Sign Up
                        </a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="mobile-menu-icon text-gray-600 hover:text-pink-600 transition-colors duration-300 p-2">
                        <svg class="w-6 h-6 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="{{ url_for('home') }}" class="mobile-nav-link">Home</a>
                <a href="{{ url_for('category', slug='fashion') }}" class="mobile-nav-link">Fashion</a>
                <a href="{{ url_for('category', slug='beauty') }}" class="mobile-nav-link">Beauty</a>
                <a href="{{ url_for('category', slug='home') }}" class="mobile-nav-link">Home</a>
                <a href="{{ url_for('category', slug='tech') }}" class="mobile-nav-link">Tech</a>
                <a href="{{ url_for('category', slug='pharmacy') }}" class="mobile-nav-link">Pharmacy</a>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
                {% for category, message in messages %}
                    {% if category == 'success' %}
                        <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-4 flash-message shadow-lg">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle mr-3 text-green-600"></i>
                                <span class="font-medium">{{ message }}</span>
                            </div>
                        </div>
                    {% elif category == 'error' %}
                        <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4 flash-message shadow-lg">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle mr-3 text-red-600"></i>
                                <span class="font-medium">{{ message }}</span>
                            </div>
                        </div>
                    {% else %}
                        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-6 py-4 rounded-lg mb-4 flash-message shadow-lg">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle mr-3 text-blue-600"></i>
                                <span class="font-medium">{{ message }}</span>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-indigo-900 text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='images/logo.png') }}"
                             alt="Amazora Logo"
                             class="h-12 w-auto logo-glow">
                        <h3 class="text-3xl font-bold bg-gradient-to-r from-blue-300 to-white bg-clip-text text-transparent">Amazora</h3>
                    </div>
                    <p class="text-indigo-200">Shop smart. Live beautifully. Your curated destination for quality and elegance.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Categories</h4>
                    <ul class="space-y-2 text-indigo-200">
                        <li><a href="{{ url_for('category', slug='fashion') }}" class="hover:text-white transition-colors duration-300">Fashion</a></li>
                        <li><a href="{{ url_for('category', slug='beauty') }}" class="hover:text-white transition-colors duration-300">Beauty</a></li>
                        <li><a href="{{ url_for('category', slug='home') }}" class="hover:text-white transition-colors duration-300">Home</a></li>
                        <li><a href="{{ url_for('category', slug='tech') }}" class="hover:text-white transition-colors duration-300">Tech</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Customer Care</h4>
                    <ul class="space-y-2 text-indigo-200">
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Shipping Info</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Returns</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">FAQ</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Connect</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-indigo-200 hover:text-white transition-colors duration-300">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-indigo-200 hover:text-white transition-colors duration-300">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-indigo-200 hover:text-white transition-colors duration-300">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z.017-.001z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-indigo-800 mt-8 pt-8 text-center text-indigo-200">
                <p>&copy; 2024 Amazora. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Cart Drawer -->
    <div id="cart-drawer" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50" id="cart-overlay"></div>
        <div class="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl transform translate-x-full transition-transform duration-300" id="cart-content">
            <div class="flex items-center justify-between p-4 border-b">
                <h2 class="text-lg font-semibold">Shopping Cart</h2>
                <button id="close-cart" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-4">
                <div id="cart-items">
                    <!-- Cart items will be loaded here -->
                </div>
            </div>
            <div class="border-t p-4">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-lg font-semibold">Total: $<span id="cart-total">0.00</span></span>
                </div>
                <a href="{{ url_for('cart') }}" class="w-full bg-gradient-to-r from-indigo-900 to-pink-600 text-white py-2 px-4 rounded-lg hover:opacity-90 transition-opacity duration-300 text-center block">
                    View Cart & Checkout
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript optimizations -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="{{ url_for('static', filename='js/main.js') }}?v=1.2" defer></script>

    <!-- Performance monitoring -->
    <script>
        // Faster loading screen hide
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => loadingScreen.style.display = 'none', 300);
                }
            }, 500); // Reduced from 2000ms to 500ms
            
            // Update cart count
            updateCartCount();
        });

        // Preload critical images
        const criticalImages = [
            '{{ url_for("static", filename="images/logo.png") }}',
            '{{ url_for("static", filename="images/favicon.svg") }}'
        ];

        criticalImages.forEach(src => {
            const img = new Image();
            img.src = src;
        });

        // Function to update cart count
        function updateCartCount() {
            fetch('/api/cart-count')
                .then(response => response.json())
                .then(data => {
                    const cartCount = document.getElementById('cart-count');
                    if (cartCount) {
                        cartCount.textContent = data.count;
                        if (data.count > 0) {
                            cartCount.style.display = 'flex';
                        } else {
                            cartCount.style.display = 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error updating cart count:', error);
                });
        }

        // Update cart count after adding items
        window.addEventListener('cartUpdated', function() {
            updateCartCount();
        });
        
        // Auto-hide flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.transition = 'opacity 0.5s ease-out';
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 500);
                }, 5000);
            });
        });
    </script>
</body>
</html>
