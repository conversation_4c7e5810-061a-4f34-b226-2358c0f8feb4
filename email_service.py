#!/usr/bin/env python3
"""
Email Service for Amazora E-commerce
Handles welcome emails and order confirmations with beautiful HTML templates
"""

from flask import render_template, url_for
from flask_mail import Mail, Message
from threading import Thread
import os

# Email configuration
mail = Mail()

def init_email_service(app):
    """Initialize email service with Flask app"""
    app.config['MAIL_SERVER'] = 'smtp.gmail.com'
    app.config['MAIL_PORT'] = 587
    app.config['MAIL_USE_TLS'] = True
    app.config['MAIL_USERNAME'] = '<EMAIL>'
    # Prefer environment variable; fall back to provided App Password (no spaces)
    app.config['MAIL_PASSWORD'] = os.environ.get('EMAIL_PASSWORD', 'ygnyrwoyglhieskk')
    app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'
    
    mail.init_app(app)

def send_async_email(app, msg):
    """Send email asynchronously"""
    with app.app_context():
        mail.send(msg)

def send_email(subject, recipients, template, **kwargs):
    """Send email with template"""
    from app import app  # Import here to avoid circular imports
    
    msg = Message(subject, recipients=recipients)
    msg.html = render_template(f'emails/{template}.html', **kwargs)
    
    # Send email asynchronously
    Thread(target=send_async_email, args=(app, msg)).start()

def send_welcome_email(user):
    """Send welcome email to new user"""
    subject = "🎉 Welcome to Amazora - Your Premium Shopping Destination!"
    send_email(
        subject=subject,
        recipients=[user.email],
        template='welcome',
        user=user,
        shop_url=url_for('home', _external=True)
    )

def send_order_confirmation_email(user, order):
    """Send order confirmation email"""
    subject = f"✅ Order Confirmed! Order #{order.id} - Amazora"
    send_email(
        subject=subject,
        recipients=[user.email],
        template='order_confirmation',
        user=user,
        order=order,
        shop_url=url_for('home', _external=True),
        profile_url=url_for('user_profile', _external=True)
    )
