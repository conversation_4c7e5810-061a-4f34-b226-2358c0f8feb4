{% extends "admin/base.html" %}

{% block title %}Products Management - Admin Dashboard{% endblock %}
{% block page_title %}Products Management{% endblock %}

{% block content %}
<div class="mb-6">
    <a href="{{ url_for('admin_add_product') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
        <i class="fas fa-plus mr-2"></i>
        Add New Product
    </a>
</div>

<div class="admin-card bg-white rounded-xl shadow-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">All Products</h3>
            <div class="text-sm text-gray-600">
                Total: {{ products|length }} products
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for product in products %}
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden mr-4">
                                {% if product.image_url %}
                                    <img src="{{ product.image_url | image_url }}" alt="{{ product.name }}" class="w-full h-full object-cover">
                                {% else %}
                                    <div class="w-full h-full bg-gradient-to-r from-gray-300 to-gray-400 flex items-center justify-center">
                                        <i class="fas fa-image text-gray-500"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                                <div class="text-sm text-gray-500">{{ product.slug }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ product.category.name }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${{ "%.2f"|format(product.price) }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if product.stock > 10 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ product.stock }} in stock
                            </span>
                        {% elif product.stock > 0 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                {{ product.stock }} low stock
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Out of stock
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if product.featured %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                <i class="fas fa-star mr-1"></i>
                                Featured
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Regular
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <a href="{{ url_for('admin_edit_product', product_id=product.id) }}" class="text-indigo-600 hover:text-indigo-900 transition-colors duration-200">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </a>
                            <form method="POST" action="{{ url_for('admin_delete_product', product_id=product.id) }}" class="inline" onsubmit="return confirmDelete('Are you sure you want to delete this product?')">
                                <button type="submit" class="text-red-600 hover:text-red-900 transition-colors duration-200">
                                    <i class="fas fa-trash mr-1"></i>
                                    Delete
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if not products %}
    <div class="text-center py-12">
        <i class="fas fa-box text-gray-400 text-4xl mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
        <p class="text-gray-500 mb-4">Create your first product to start selling.</p>
        <a href="{{ url_for('admin_add_product') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200">
            <i class="fas fa-plus mr-2"></i>
            Add First Product
        </a>
    </div>
    {% endif %}
</div>

<!-- Product Statistics -->
<div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
    <div class="admin-card bg-white rounded-xl p-6 shadow-lg">
        <div class="flex items-center">
            <div class="bg-blue-100 rounded-full p-3 mr-4">
                <i class="fas fa-box text-blue-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Total Products</p>
                <p class="text-2xl font-bold text-gray-900">{{ products|length }}</p>
            </div>
        </div>
    </div>

    <div class="admin-card bg-white rounded-xl p-6 shadow-lg">
        <div class="flex items-center">
            <div class="bg-purple-100 rounded-full p-3 mr-4">
                <i class="fas fa-star text-purple-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Featured Products</p>
                <p class="text-2xl font-bold text-gray-900">{{ products|selectattr('featured')|list|length }}</p>
            </div>
        </div>
    </div>

    <div class="admin-card bg-white rounded-xl p-6 shadow-lg">
        <div class="flex items-center">
            <div class="bg-green-100 rounded-full p-3 mr-4">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">In Stock</p>
                <p class="text-2xl font-bold text-gray-900">{{ products|selectattr('stock', 'gt', 0)|list|length }}</p>
            </div>
        </div>
    </div>

    <div class="admin-card bg-white rounded-xl p-6 shadow-lg">
        <div class="flex items-center">
            <div class="bg-red-100 rounded-full p-3 mr-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Out of Stock</p>
                <p class="text-2xl font-bold text-gray-900">{{ products|selectattr('stock', 'eq', 0)|list|length }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
